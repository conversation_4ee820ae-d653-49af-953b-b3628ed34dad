import {
    type editor as <PERSON><PERSON><PERSON><PERSON>,
    EditorScopedLayoutService,
    IAccessibilityService,
    IAccessibilitySignalService,
    IClipboardService,
    ICodeEditorService,
    ICommandService,
    IConfigurationService,
    IContextKeyService,
    IContextMenuService,
    IContextViewService,
    IEditorProgressService,
    IEditorWorkerService,
    IHoverService,
    IInstantiationService,
    IKeybindingService,
    ILanguageConfigurationService,
    ILanguageFeaturesService,
    ILanguageService,
    ILayoutService,
    IModelService,
    INotificationService,
    IOpenerService,
    IQuickInputService,
    IStandaloneThemeService,
    OpenerService,
    QuickInputService,
    ServiceCollection,
    StandaloneDiffEditor,
    StandaloneEditor,
    StandaloneServices,
    StaticServices,
} from 'mo/monaco';
import { inject, injectable } from 'tsyringe';

import { ColorThemeService } from './colorTheme';

type IEditorOverrideServices = MonacoEditor.IEditorOverrideServices;

@injectable()
export class MonacoService {
    private _services: ServiceCollection;
    private _container!: HTMLElement | null;
    private _hiddenEditor: MonacoEditor.IStandaloneCodeEditor | null = null;
    private _hiddenEditorContainer: HTMLElement | null = null;
    private _hiddenEditorStyle: HTMLStyleElement | null = null;
    private _focusedEditor: MonacoEditor.IStandaloneCodeEditor | null = null;

    constructor(@inject('colorTheme') private colorTheme: ColorThemeService) {}

    public initWorkspace(container: HTMLElement) {
        this._container = container;
        this._services = this.createStandaloneServices();
    }

    get container() {
        return this._container;
    }

    get services() {
        return this._services;
    }

    get commandService() {
        return this.services.get(ICommandService);
    }

    get QuickInputService(): IQuickInputService {
        return this.services.get(IQuickInputService);
    }

    private mergeEditorServices(overrides?: IEditorOverrideServices) {
        if (overrides) {
            const services = this.services;
            for (const serviceId in overrides) {
                if (serviceId) {
                    const service = services.get(serviceId);
                    if (service && overrides[serviceId]) {
                        services.set(serviceId, overrides[serviceId]);
                    }
                }
            }
        }
    }

    public create(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneCodeEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneEditor = new StandaloneEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(ICodeEditorService),
            services.get(ICommandService),
            services.get(IContextKeyService),
            services.get(IHoverService),
            services.get(IKeybindingService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IAccessibilityService),
            services.get(IModelService),
            services.get(ILanguageService),
            services.get(ILanguageConfigurationService),
            services.get(ILanguageFeaturesService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        // Track editor focus for smart focus management
        this.setupEditorFocusTracking(standaloneEditor);

        return standaloneEditor;
    }

    public createDiffEditor(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneDiffEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneDiffEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneDiffEditor = new StandaloneDiffEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(IContextKeyService),
            services.get(ICodeEditorService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IContextMenuService),
            services.get(IEditorProgressService),
            services.get(IClipboardService),
            services.get(IAccessibilitySignalService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneDiffEditor;
    }

    // When Application will unmount, call it
    public dispose() {
        this.disposeHiddenEditor();
    }

    /**
     * Create a hidden editor to ensure QuickInputService always has a focused editor context
     */
    private createHiddenEditor() {
        try {
            // Create a hidden container for the editor
            this._hiddenEditorContainer = document.createElement('div');
            this._hiddenEditorContainer.className = 'mo-hidden-editor-container';
            document.body.appendChild(this._hiddenEditorContainer);

            // Create a style element to hide the editor
            this._hiddenEditorStyle = document.createElement('style');
            this._hiddenEditorStyle.textContent = `
                .mo-hidden-editor-container { pointer-events: none; position: fixed; left: -9999px; top: -9999px; width: 100vw; height: 100vh; z-index: 9999; }
                .mo-hidden-editor-container .monaco-editor { background-color: transparent; outline: none; }
                .mo-hidden-editor-container .monaco-editor > *:not(.overflow-guard) { opacity: 0; }
                .mo-hidden-editor-container .monaco-editor .overflow-guard > *:not(.overlayWidgets) { opacity: 0; }
                .mo-hidden-editor-container .monaco-editor .overflow-guard .overlayWidgets { pointer-events: auto; }
            `;
            document.head.appendChild(this._hiddenEditorStyle);

            // Create the hidden editor with minimal configuration
            this._hiddenEditor = new StandaloneEditor(
                this._hiddenEditorContainer,
                {
                    readOnly: true,
                    minimap: { enabled: false },
                    scrollbar: { vertical: 'hidden', horizontal: 'hidden' },
                    lineNumbers: 'off',
                },
                this.services.get(IInstantiationService),
                this.services.get(ICodeEditorService),
                this.services.get(ICommandService),
                this.services.get(IContextKeyService),
                this.services.get(IHoverService),
                this.services.get(IKeybindingService),
                this.services.get(IStandaloneThemeService),
                this.services.get(INotificationService),
                this.services.get(IConfigurationService),
                this.services.get(IAccessibilityService),
                this.services.get(IModelService),
                this.services.get(ILanguageService),
                this.services.get(ILanguageConfigurationService),
                this.services.get(ILanguageFeaturesService)
            );

            // Listen for keydown events with smart focus management
            document.addEventListener('keydown', this.handleGlobalKeydown.bind(this));
        } catch (error) {
            console.warn('Failed to create hidden editor:', error);
        }
    }

    private disposeHiddenEditor() {
        if (this._hiddenEditor) {
            this._hiddenEditor.dispose();
            this._hiddenEditor = null;
        }
        if (this._hiddenEditorContainer && this._hiddenEditorContainer.parentNode) {
            this._hiddenEditorContainer.parentNode.removeChild(this._hiddenEditorContainer);
            this._hiddenEditorContainer = null;
        }
        if (this._hiddenEditorStyle && this._hiddenEditorStyle.parentNode) {
            this._hiddenEditorStyle.parentNode.removeChild(this._hiddenEditorStyle);
            this._hiddenEditorStyle = null;
        }
        document.removeEventListener('keydown', this.handleGlobalKeydown);

        // Clear focused editor reference
        this._focusedEditor = null;
    }

    /**
     * Ensure the hidden editor has focus when QuickInputService operations are needed
     * This method should be called before any QuickInputService operations
     */
    public ensureQuickInputContext(): void {
        if (this._hiddenEditor && this._hiddenEditorContainer) {
            try {
                this._hiddenEditor.focus();
            } catch (error) {
                console.warn('Failed to focus hidden editor:', error);
            }
        } else {
            console.warn('MonacoService: Hidden editor not available, QuickInputService may not work properly');
        }
    }

    private createStandaloneServices(): ServiceCollection {
        const instantiationService = StandaloneServices.initialize({});
        const services = new ServiceCollection();
        const serviceIds = [
            IInstantiationService,
            ICodeEditorService,
            ICommandService,
            IConfigurationService,
            IContextKeyService,
            IKeybindingService,
            IContextViewService,
            IStandaloneThemeService,
            INotificationService,
            IAccessibilityService,
            IAccessibilitySignalService,
            IModelService,
            ILanguageService,
            ILanguageConfigurationService,
            ILanguageFeaturesService,
            IHoverService,
            IEditorWorkerService,
            IContextMenuService,
            IEditorProgressService,
            IClipboardService,
        ];

        serviceIds.forEach((serviceId) => {
            const service = StandaloneServices.get(serviceId);
            if (service) {
                services.set(serviceId, service);
            }
        });

        if (!services.get(IOpenerService)) {
            services.set(
                IOpenerService,
                new OpenerService(services.get(ICodeEditorService), services.get(ICommandService))
            );
        }

        const quickInputService = instantiationService.createInstance(QuickInputService);
        const layoutService = new EditorScopedLayoutService(
            this.container,
            StaticServices.codeEditorService.get(ICodeEditorService)
        );

        // Override layoutService
        services.set(ILayoutService, layoutService);

        // Override quickPickService
        services.set(IQuickInputService, quickInputService);

        // Override dispose for prevent disposed by instance
        this.dispose = services.dispose;
        services.dispose = () => {};

        // Create hidden editor after services are set up
        // Use RAF to ensure DOM is ready and services are fully initialized
        window.requestAnimationFrame(() => {
            this.createHiddenEditor();
        });

        return services;
    }

    /**
     * Setup focus tracking for a regular editor instance
     */
    private setupEditorFocusTracking(editor: MonacoEditor.IStandaloneCodeEditor): void {
        editor.onDidFocusEditorText(() => {
            this._focusedEditor = editor;
        });

        editor.onDidBlurEditorText(() => {
            // Clear focused editor reference when editor loses focus
            if (this._focusedEditor === editor) {
                this._focusedEditor = null;
            }
        });
    }

    /**
     * Handle global keydown events with smart focus management
     */
    private handleGlobalKeydown(e: KeyboardEvent): void {
        // Only handle events with modifier keys
        if (!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)) {
            return;
        }

        // No regular editor has focus, ensure QuickInput context for all modifier key combinations
        if (!this._focusedEditor || !this._focusedEditor.hasTextFocus()) {
            this.ensureQuickInputContext();
        }
    }
}
